"use client";
import React, { useEffect, useState } from "react";
import { Input, Button, message } from "antd";
import Image from "next/image";
import { Link } from "@/navigation";
import { <PERSON>ed<PERSON><PERSON>ogo, <PERSON>iktokLogo } from "@phosphor-icons/react";
import { useCategory } from "@/context/CategoriesContext";
import FollowUs from "@/components/Social/FollowUs";
import { useLocale, useTranslations } from "next-intl";
const { Search } = Input;
import { Menu, menus } from "@/lib/menu";
import { contactInfo, payarr, FOLLOW } from "@/lib/contacts";
import Follow from "../Social/Follow";
import { useModalCustomerContext } from "@/context/CustomerContext";
import SliderBar from "../SliderBar";
import { subscribeEmail } from "@/lib/api/subscribe";
import { defaultLocale } from "@/config";
import clsx from "clsx";
import Collapse from "../Collapse";
import Indicator from "../Indicator";
import useIsMobile from "@/lib/hooks/useIsMobile";
import SEOOptimizedImage from "@/components/Image/SEOOptimizedImage";
import { YoutubeOutlined } from '@ant-design/icons'
const Footer = ({ categories }) => {
	let locale = useLocale();

	// 创建动态的分类展开状态对象
	const [categoryOpenState, setCategoryOpenState] = useState({});

	//基本展开状态
	const [isOpen, setIsOpen] = useState({
		isQuickIinks: true,
		isProducts: true,
		isCompany: true,
		isSupport: true, // 添加支持列的展开状态
	});

	const t = useTranslations();
	const [subscribeLoading, setSubscribeLoading] = useState(false);
	const [isSubscribeSuccess, setIsSubscribeSuccess] = useState(false);
	const [isSubscribeError, setIsSubscribeError] = useState(false);
	let isMobile = useIsMobile()

	// 初始化分类展开状态
	useEffect(() => {
		if (categories && categories.edges) {
			// 创建分类展开状态对象
			const initialState = {};
			categories.edges.forEach((category, index) => {
				// 为每个分类设置一个唯一的键
				initialState[`category_${category.node.id}`] = !isMobile;
			});

			setCategoryOpenState(initialState);
		}

		// 设置基本展开状态
		if (isMobile) {
			setIsOpen({
				isQuickIinks: false,
				isProducts: false,
				isCompany: false,
				isSupport: false, // 移动端默认折叠
			});
		} else {
			setIsOpen({
				isQuickIinks: true,
				isProducts: true,
				isCompany: true,
				isSupport: true, // 桌面端默认展开
			});
		}
	}, [isMobile, categories]);

	// 切换特定分类的展开状态
	const toggleCategoryOpen = (categoryId) => {
		setCategoryOpenState(prevState => ({
			...prevState,
			[`category_${categoryId}`]: !prevState[`category_${categoryId}`]
		}));
	};

	const subscribe = async (value: string) => {
		try {
			if (!value) return;
			setSubscribeLoading(true);
			await subscribeEmail({ email: value, subscribe_type: 3 });
			setIsSubscribeSuccess(true);
		} catch (e) {
			message.error(e.message);
			setIsSubscribeError(true);
		} finally {
			setSubscribeLoading(false);
		}
	};

	const [showSuccess, setShowSuccess] = useState(false);

	useEffect(() => {
		if (isSubscribeSuccess) {
			setShowSuccess(true);
			const timer = setTimeout(() => {
				setShowSuccess(false);
				setIsSubscribeSuccess(false);
			}, 3000); // 3秒后淡出
			return () => clearTimeout(timer);
		}
	}, [isSubscribeSuccess]);
	const navigation = {
		QUICKLINKS: [
			{ id: 3, name: "menu.blog", link: "/blog", hasSlug: true, show: true },
			{ id: 4, name: "menu.aboutUs", link: "/about-us", hasSlug: true, show: true },
			{ id: 5, name: "menu.contactUs", link: "/contact-us", hasSlug: true, show: true },
			// { id: 6, name: "menu.faqs", link: "/faqs", hasSlug: true, show: true },
		],
		SUPPORT: [
			{ id: 1, name: t("menu.becr"), link: "/retailer", show: true },
			// { id: 2, name: t("menu.faq"), link: "/faqs", show: true },
			{ id: 3, name: t("menu.op"), link: "/orders-payments", show: true },
			{ id: 4, name: t("menu.de"), link: "/delivery", show: true },
			{ id: 5, name: t("menu.rn"), link: "/returns", show: true },
			{ id: 6, name: t("menu.pi"), link: "/instructions", show: true },
			{ id: 7, name: t("menu.war"), link: "/warranty", show: true },
			{ id: 8, name: t("menu.term"), link: "/terms", show: true },
			// { id: 9, name: t("menu.ap"), link: "/auction-policy", show: true },
		],
		contact: [
			{
				name: t(`menu.Emall`),
				href: `mailto:${contactInfo.email}`,
				value: `${contactInfo.email}`,
			},
			{
				name: t(`menu.Address`),
				href: `${contactInfo.addressLink}`,
				value: `${contactInfo.address}`,
			},
		],
	};

	return (
		<footer className="footer bg-black text-white max-md:pb-[70px]">
			<div className="container px-4 py-[40px] pt-[66px] text-[14px] max-md:py-[33px]">
				{/* 整体 grid 布局 */}
				<div className="grid gap-6 2xl:grid-cols-12 2xl:gap-6  xl:gap-4  lg:gap-4 md:grid-cols-2 md:gap-8 sm:grid-cols-1 sm:gap-4">
					{/* 第一部分 - Logo 和订阅 */}
					<div className="2xl:col-span-3 md:col-span-1 sm:col-span-1">
						<Image
							src="/image/footer-logo-r.png"
							width={250}
							height={21}
							priority
							alt={`${process.env.NEXT_PUBLIC_COMPANY_NAME} footer logo`}
							className="object-contain mb-6 w-[240px] h-[50px]"
							unoptimized
						></Image>
						<div className="mb-6">
							<h3 className="text-white text-sm mb-4 irs font-semibold">{t("footer.signUpLetter")}</h3>
							<SearchInput onSearch={subscribe} loading={subscribeLoading} isSubscribeSuccess={isSubscribeSuccess} showSuccess={showSuccess} />
						</div>
						<div className="flex gap-2 flex-wrap">
							{
								FOLLOW.map(item => (
									<div key={item.id} className="group">
										<div className="relative w-[28px] h-[28px]">
											<Image
												src={item.image}
												width={40}
												height={40}
												alt={item.name}
												className="transition-opacity duration-200 group-hover:opacity-0 w-full h-full object-contain"
											/>
											<Image
												src={item.image_hover}
												width={40}
												height={40}
												alt={item.name}
												className="absolute top-0 left-0 transition-opacity duration-200 opacity-0 group-hover:opacity-100 w-full h-full object-contain"
											/>
										</div>
									</div>
								))
							}
							<div className="w-[28px] h-[28px] bg-[#858788] group hover:bg-[#ff0033] rounded-full flex items-center justify-center">
								<span className="text-black text-base group-hover:text-white"><YoutubeOutlined /></span>
							</div>
							<div className="w-[28px] h-[28px] bg-[#858788] group hover:bg-[#0c61bf] rounded-full flex items-center justify-center relative overflow-hidden">
								<Image
									src="/image/footer-follow/in.png"
									alt="linkedin"
									width={100}
									height={100}
									className="w-[13px] h-[13px] object-contain transition-opacity duration-200 group-hover:opacity-0"
								/>
								<Image
									src="/image/footer-follow/in21.png"
									alt="linkedin"
									width={100}
									height={100}
									className="w-[13px] h-[13px] object-contain absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 transition-opacity duration-200 opacity-0 group-hover:opacity-100"
								/>
							</div>
						</div>
					</div>

					{/* 动态渲染分类 */}
					{categories && categories.edges && categories.edges.length > 0 &&
						(() => {
							const targetSlugs = ['paddle', 'accessories', 'apparel'];
							const categoryMap = new Map(categories.edges.map(edge => [edge.node.slug, edge]));
							const targetCategories = targetSlugs.map(slug => categoryMap.get(slug)).filter(Boolean);

							const categoriesToRender = targetCategories.length === targetSlugs.length
								? targetCategories : categories.edges;

							return categoriesToRender.map((category, index) => {
								const categoryId = category.node.id;
								const categoryName = locale === defaultLocale ? category.node.name : (category.node.translation?.name || category.node.name);
								const hasChildren = category.node.children && category.node.children.edges && category.node.children.edges.length > 0;
								const isOpenKey = `category_${categoryId}`;

								return (
									<div key={categoryId} className={`${(index === 1 || index === 2) ? "2xl:col-span-1" : "2xl:col-span-2"} 2xl:pt-5 ${index===1?"2xl:-ml-[50px]":""} ${index === 0?"2xl:pl-[20px]":""}`}>
										<div className="max-md:flex max-md:justify-between">
											<h3 className="mb-4 text-[14px] uppercase text-white max-sm:mb-3">
												{categoryName}
											</h3>
											<i
												className={clsx("hidden max-md:block text-xl",
													!categoryOpenState[isOpenKey] ? "ri-add-line" : "ri-subtract-fill"
												)}
												onClick={() => toggleCategoryOpen(categoryId)}
											></i>
										</div>
										<Collapse open={categoryOpenState[isOpenKey]}>
											<ul className="space-y-3">
												{/* 二级分类链接 */}
												{hasChildren && category.node.children.edges.map((child) => {
													const childName = locale === defaultLocale ? child.node.name : (child.node.translation?.name || child.node.name);
													return (
														<li key={child.node.id}>
															<Link
																href={`/products/${child.node.slug}`}
																className="text-[#888] hover:text-white transition-colors irs"
															>
																{childName}
															</Link>
														</li>
													);
												})}
											</ul>
										</Collapse>
									</div>
								);
							});
						})()
					}
					{/* COMPANY */}
					<div className="2xl:pl-[10px] 2xl:pt-5 2xl:col-span-1 xl:col-span-1 lg:col-span-1">
						<div className="max-md:flex max-md:justify-between">
							<h3 className="mb-4 text-[14px] uppercase text-white max-md:mb-3">{t("menu.company")}</h3>
							<i className={clsx("hidden max-md:block text-xl", !isOpen.isCompany ? "ri-add-line" : "ri-subtract-fill")} onClick={() => setIsOpen(prevState => ({ ...prevState, isCompany: !prevState.isCompany }))}></i>
						</div>
						<Collapse open={isOpen.isCompany}>
							<ul className="space-y-3">
								{navigation.QUICKLINKS.map((item) => {
									return (
										item.show && (
											<li key={item.id}>
												<Link href={item.link} className="text-[#888] hover:text-white transition-colors irs">
													{t(item.name)}
												</Link>
											</li>
										)
									);
								})}
							</ul>
						</Collapse>
					</div>

					{/* SUPPORT - 新增列 */}
					<div className="2xl:pl-[10px] 2xl:pt-5 2xl:col-span-2 2xl:ml-[15px]">
						<div className="max-md:flex max-md:justify-between">
							<h3 className="mb-4 text-[14px] uppercase text-white max-md:mb-3">{t("menu.support")}</h3>
							<i className={clsx("hidden max-md:block text-xl", !isOpen.isSupport ? "ri-add-line" : "ri-subtract-fill")} onClick={() => setIsOpen(prevState => ({ ...prevState, isSupport: !prevState.isSupport }))}></i>
						</div>
						<Collapse open={isOpen.isSupport}>
							<ul className="space-y-3">
								{navigation.SUPPORT.map((item) => {
									return (
										item.show && (
											<li key={item.id}>
												<Link href={item.link} className="text-[#888] hover:text-white transition-colors irs">
													{item.name}
												</Link>
											</li>
										)
									);
								})}
							</ul>
						</Collapse>
					</div>

					{/* CONTACT US */}
					<div className="2xl:pt-5 2xl:col-span-2 2xl:-ml-[20px]">
						<h3 className="mb-4 text-[14px]  uppercase text-white max-md:mb-3">{t("menu.contact_Us")}</h3>
						<div className="space-y-3">
							{navigation.contact.map((item, index) => {
								return (
									<div key={index} className={`text-[#888] irs ${item.value === contactInfo.email ? "whitespace-nowrap" : ""}`}>
										{item.name && <span className="text-[#888]">{item.name}: </span>}
										{item.href ? (
											<Link href={item.href} className={`hover:text-white transition-colors text-[#888] ${item.value === contactInfo.email ? "whitespace-nowrap" : ""}`} target="_blank">
												{item.value}
											</Link>
										) : (
											<span>{item.value}</span>
										)}
									</div>
								);
							})}
						</div>
					</div>
				</div>
			</div>

			<div className="container">
				<div className="  flex items-center justify-between max-md:flex-col max-md:justify-center max-md:gap-y-5 border-t border-gray-700 py-4 text-[14px] text-ddd">
					<span>{t("footer.copyright")}</span>
					<span className="text-lg text-white flex gap-1 " >
						{
							payarr.map(item => (<img key={item.img} src={item.img} className="w-[50px] h-[30px]" alt={item.alt} />))
						}

					</span>
				</div>
			</div>
		</footer >
	);
};

export default Footer;


function SearchInput({ onSearch, className, loading, isSubscribeSuccess, showSuccess }: any) {
	const [searchValue, setSearchValue] = useState("");
	const t = useTranslations();
	const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === "Enter") {
			onSearch(e.currentTarget.value);
		}
	};
	const toSearch = () => {
		// router.push(`/search?q=${value}`);
		onSearch(searchValue);
	};
	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchValue(e.target.value);
	};
	return (
		<div className="w-full">
			<div className={clsx("w-full h-[40px] flex gap-1", className || "")}>
				<div className="flex-1 border-2 border-white">
					<input
						value={searchValue}
						onChange={handleChange}
						onKeyDown={handleKeyDown}
						className="w-full h-full text-white pl-4 bg-transparent outline-none placeholder-white irs text-sm font-semibold"
						placeholder={t("footer.Enteryour")}
						autoComplete="off"
					/>
				</div>
				<button
					onClick={toSearch}
					type="submit"
					className="px-4 bg-white text-black hover:bg-gray-100 focus:outline-none font-medium whitespace-nowrap flex items-center justify-center min-w-[80px]"
					disabled={loading}
				>
					{loading ? (
						<span className="flex items-center">
							<svg className="animate-spin h-5 w-5 text-black" viewBox="0 0 24 24">
								<circle
									className="opacity-20"
									cx="12"
									cy="12"
									r="10"
									stroke="currentColor"
									strokeWidth="4"
									fill="none"
								/>
								<path
									className="opacity-80"
									fill="currentColor"
									d="M12 2a10 10 0 0 1 10 10h-4a6 6 0 0 0-6-6V2z"
								/>
							</svg>
							{t("nav.Subscribe")}
						</span>
					) : (
						t("nav.Subscribe")
					)}
				</button>
			</div>
			{showSuccess && (
				<div
					className=" bg-[#e5e5e5] text-black  px-6 py-3 flex items-center mt-2 shadow transition-opacity duration-500"
				>
					<span className="w-5 h-5 bg-black rounded-full flex items-center justify-center mr-2">
						<svg width="16" height="16" fill="white" viewBox="0 0 16 16"><path d="M6.003 11.803l-3.5-3.5 1.414-1.414L6 8.975l5.086-5.086 1.414 1.414z" /></svg>
					</span>
					<span className="text-[12px]">{t("message.subscribeSuccess")}</span>
				</div>
			)}
		</div>
	);
}