"use client";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";
import Step, { StepStatus } from "./Step";
import { useShoppingCartStore } from "@/lib/store/shoppingCart";
import EmptyState from "../EmptyState";
import CartContent from "./CartContent";
import CartContentBuyBow from "./CartContent-BuyBow";
import { Controller, useForm } from "react-hook-form";
import clsx from "clsx";
import { RingLoader } from "react-spinners";
import { useUserStore } from "@/store/user.store";
import { App } from "antd"; // 添加这行导入
import AccountInfo from "./AccountInfo";
import { contactInfo } from "@/lib/contacts";
import PayMeth from "./Paymeth";
import { OrderSum } from "./OrderSum";
import { Placeholder } from "../Placeholder";
import { BodyText } from "../BodyText";
import { useShoppingCart } from "@/lib/hooks/useShoppingCart";
import axios from "axios";
import {
	CheckoutAddAddress,
	CheckoutAddBillingAddress,
	EmailUpdate,
	GetShippingMethodlist,
	UpdateCheckoutMutation,
	UpdateCheckoutShippingMethod,
} from "@/lib/api/Checkout";
import { defaultLocale, isDev } from "@/lib/utils/util";
import { useRouter } from "@/navigation";
import { getAddresslist } from "@/lib/api/user";
import Collapse from "../Collapse";
import { Link } from "@/navigation";
import Airwallex from "./components/Card";
import { HomeTaile } from "../Contact/ConcatPage";
import Coupon from "./Coupon";
import { getChannelLanguageMap } from "@/lib/api/channel";
import { useUserAuth } from "@/lib/hooks/useUserAuth";
import VisitorAddress from "./VisitorAddress";

function Index() {
	const { findCheckout, BuyBowfindCheckout } = useShoppingCart();
	const { isLogin } = useUserAuth();
	const t = useTranslations();
	const [orderStatusText, setOrderStatusText] = useState<string>(
		t("message.db061d7ad349624a868aa8a585db77fccca3"),
	);
	// 选中的步骤
	const [activeId, setActiveId] = useState(1);
	// 将 message 替换为 App.useApp()
	const { message } = App.useApp();

	const stepsStatus = (id: number) => {
		const status: StepStatus[] = ["complete", "current", "incomplete"];
		if (activeId === 3) return status[0];
		if (id === activeId) return status[1];
		if (id < activeId) return status[0];
	};

	//购物车数据
	const { cartList, isCart, BuyBowcheckoutId, BuyBowCartList } = useShoppingCartStore() as any;
	//填地址
	const [showForm, setShowForm] = useState(false);
	//是否填地址
	const [isAddress, setisAddress] = useState(false);

	const { userInfo } = useUserStore();

	// 地址列表
	const [shipping, setShipping] = useState<any>(null);

	// 选中的地址
	const [activeshipping, setactiveshipping] = useState<any>(null);

	//是否展开
	const [isOpen, setIsOpen] = useState(false);

	//支付方式
	const [PaymentMethod, setPaymentMethod] = useState("paypal");

	// Airwallex 数据
	const [AirwallexDetail, setAirwallexDetail] = useState<any>(null);

	let router = useRouter();
	const {
		register,
		handleSubmit,
		setValue,
		reset,
		formState: { errors, isValid },
		getValues: getFormValues,
		control,
	} = useForm({
		mode: "onBlur",
	});

	const [loading, setLoading] = useState(false);
	const [isDataReady, setIsDataReady] = useState(false);

	useEffect(() => {
		if (cartList || BuyBowCartList) {
			setIsDataReady(true);
		}
	}, [cartList, BuyBowCartList]);

	const onSubmit = async (data: any) => {
		let List = isCart ? { ...cartList } : { ...BuyBowCartList };

		if (!List.lines.length) return message.error(t("message.1a0ec3c72a531b4791288d44a37faf6da887"));

		if (isLogin() && !activeshipping) {
			return message.error(t("message.7b47b897b847014065a39f5b807998984337a3"));
		}
		setLoading(true);
		if (activeshipping) {
			try {
				let Formdata = {
					firstName: activeshipping.firstName,
					lastName: activeshipping.lastName,
					companyName: activeshipping.companyName,
					streetAddress1: activeshipping.streetAddress1,
					city: activeshipping.city,
					countryArea: activeshipping.countryArea,
					postalCode: activeshipping.postalCode,
					country: isLogin() ? activeshipping.country.code : activeshipping.country,
					phone: activeshipping.phone,
				};

				// 购物车添加地址
				let { checkoutShippingAddressUpdate } = await CheckoutAddAddress({
					checkoutId: List.id,
					...Formdata,
				});
				let { checkoutBillingAddressUpdate } = await CheckoutAddBillingAddress({
					checkoutId: List.id,
					...Formdata,
				});

				if (checkoutShippingAddressUpdate.errors.length > 0) {
					message.error(
						checkoutShippingAddressUpdate.errors[0].field +
							"-" +
							checkoutShippingAddressUpdate.errors[0].message,
					);
					return setLoading(false);
				} else {
					setisAddress(true);
					setShowForm(false);
					// message.success(t("message.c1d1b0fe14107a4782b8836e84bcf8f55e94"));
				}
			} catch (e) {
				message.error(t("message.fb1ec174b6ac8f409ffa6ebdf48e2666cc5e"));
				return setLoading(false);
			}
		}

		try {
			//获取仓库ID
			//1.获取配送方式的数据

			let ShippingMethodid =isCart?cartList.availableCollectionPoints[0].id:BuyBowCartList.availableCollectionPoints[0].id;
    

			//更新 checkout 配送方式的 mutation
			let UpdateMutation =(await UpdateCheckoutMutation(
        ShippingMethodid,
        List.id,
      ));

			// 更新邮箱
			await EmailUpdate(List.id, !isLogin() ? activeshipping.email : userInfo.email);

			//支付方式
			if (PaymentMethod == "paypal") {
				//paypal 支付
				// console.log("showForm4", UpdateMutation);
				//获取链接
				const datapaypal = await axios.post(
					`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/payment/create_paypal_payment`,
					{
						checkout_id: List.id,
					},
				);
				setLoading(false);
				setActiveId(3);
				// console.log("showForm5", datapaypal);
				if (datapaypal.data.code == 200) {
					setTimeout(() => {
						let PalURL = datapaypal.data.detail.approval_url;
						if (PalURL) {
							return window.open(PalURL, isDev ? "_blank" : "_self");
						}
						message.error(datapaypal.data.message);
					}, 500);
				}
			} else {
				//创建airwallex_DATA
				const { data } = await axios.post(
					`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/payment/create_airwallex_payment`,
					{
						checkout_id: List.id,
					},
				);

				console.log(data, "data received");
				setLoading(false);
				if (data.code == 200) {
					setAirwallexDetail(data.detail);
					setActiveId(2);
				}
			}
		} catch (e) {
			console.log(e.message);
		} finally {
			return setLoading(false);
		}
	};
	useEffect(() => {
		getdata();
	}, []);

	//获取数据列表
	async function getdata() {
		if (!isLogin()) {
			return;
		}
		setLoading(true);
		let data = await getAddresslist(userInfo.token);
		if (data.me.addresses && data.me.addresses.length > 0) {
			// 设置地址信息
			setShipping(data.me.addresses);
			let filterdata = data.me.addresses.filter((address) => address.isDefaultShippingAddress)[0] || null;
			// console.log(data.me.addresses, "data.me.addresses", filterdata);
			setactiveshipping(filterdata);
		}
		setLoading(false);
	}

	function changeIsOpen() {
		if (isLogin()&&shipping) {
			setIsOpen(!isOpen);
		} else {
			router.push("/center");
		}
	}
	//airwallex 回调
	function AirwallexCallback() {
		setActiveId(3);
	}

	const MAX_DURATION = 2 * 60 * 1000; // 2分钟
	const INTERVAL = 4000; // 4秒
	useEffect(() => {
		let intervalId: NodeJS.Timeout;
		let startTime: number;

		async function getQuerId() {
			if (PaymentMethod == "paypal") {
				return;
			}

			try {
				const { data } = await axios.get(
					`${process.env.NEXT_PUBLIC_LANGUAGE_LIST_URL}/payment/get_checkout_status?query_id=${AirwallexDetail.query_id}`,
				);

				// console.log(data, 'airwallex_DATA');

				if (data.code === 200) {
					// 支付成功，跳转并清除定时器
					if (data.detail.payment_status === "success") {
						clearInterval(intervalId);
						data.detail.order_id && router.push(`/paypal-state?checkout_id=${data.detail.order_id}`);
						return;
					}
				}

				// 检查是否超过2分钟
				if (Date.now() - startTime >= MAX_DURATION) {
					clearInterval(intervalId);
					console.log("轮询：已经过了2分钟");
				}
			} catch (error) {
				console.error("查询订单状态出错：", error);
			}
		}

		if (activeId === 3) {
			startTime = Date.now(); // 记录开始时间
			getQuerId(); // 立即执行一次
			intervalId = setInterval(getQuerId, INTERVAL);

			// 清理函数
			return () => {
				clearInterval(intervalId);
			};
		}
	}, [activeId]);

	return (
		<div>
			<HomeTaile msg={t("checkout.Checkout")} />
			<div className="bg-themeSecondary100 pt-6">
				<Step
					steps={[
						{
							id: 1,
							name: t("message.cfe62e95200fa44dc3f8874281d7421873f4"),
							active: activeId === 1,
							status: stepsStatus(1),
							children: (
								<div className="container">
									{!isDataReady ? (
										<div className="flex h-[50vh] items-center justify-center py-20">
											<RingLoader color="#f39700" size={40} />
										</div>
									) : cartList?.lines?.length > 0 || BuyBowCartList?.lines?.length > 0 ? (
										<div className="flex flex-col pb-28 pt-8">
											<div className="mx-auto mb-5 w-full">
												<div className="relative overflow-hidden rounded-xl bg-white p-6 shadow-md">
													<div className=" ">
														<div className="mb-6 flex rounded bg-themeLightGray p-2.5 text-xl font-medium text-themeDark">
															<h3 className="ml-2 w-full sm:w-5/12 md:w-4/12">
																{t("checkout.86317421d9bc62436858c0333136cb6c40e6")}
															</h3>
														</div>

														{isLogin() ? (
															<>
																{/* 触发按钮 - 选中的地址展示 */}
																<div
																	onClick={() => changeIsOpen()}
																	className="relative box-border flex w-full cursor-pointer items-center justify-between rounded-lg border-[1px] border-main bg-white px-6 py-4 shadow-sm transition-all duration-300 hover:shadow-md"
																>
																	{activeshipping ? (
																		<div className="flex flex-col gap-2">
																			{/* 收件人姓名 */}
																			<div className="flex items-center gap-2  ">
																				<div className="text-lg font-semibold text-main">
																					{activeshipping.firstName} {activeshipping.lastName}
																				</div>
																				{activeshipping.isDefaultShippingAddress && (
																					<span className="rounded-full bg-main/10 px-2 py-0.5 text-xs font-medium text-main">
																						{t("common.default")}
																					</span>
																				)}
																			</div>

																			{/* 地址信息 */}
																			<div className="text-sm text-gray-700 ">
																				<div className="mb-2">
																					{activeshipping.companyName} {activeshipping.streetAddress1}
																				</div>
																				<div className="mb-2">
																					{[
																						activeshipping.city,
																						activeshipping.countryArea,
																						activeshipping.postalCode,
																					]
																						.filter(Boolean)
																						.join(", ")}{" "}
																					{activeshipping.country.country}
																				</div>
																			</div>

																			{/* 电话号码 */}
																			<div className="flex items-center gap-2 text-sm text-gray-600">
																				<svg
																					className="h-4 w-4"
																					fill="none"
																					stroke="currentColor"
																					viewBox="0 0 24 24"
																				>
																					<path
																						strokeLinecap="round"
																						strokeLinejoin="round"
																						strokeWidth={2}
																						d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
																					/>
																				</svg>
																				{activeshipping.phone}
																			</div>
																		</div>
																	) : (
																		<div className="py-2 text-gray-500">
																			{t("nav.no default address is set")}
																		</div>
																	)}

																	<div className="flex items-center gap-2">
																		{activeshipping ? (
																			<span className="text-sm font-medium text-main">
																				{isOpen ? t("nav.close") : t("nav.change")}
																			</span>
																		) : (
																			<Link href="/center" className="text-gray-500">
																				<span className="text-sm font-medium text-main">
																					{t("nav.Add address")}{" "}
																				</span>
																			</Link>
																		)}

																		{activeshipping && (
																			<svg
																				className={`h-5 w-5 text-main transition-transform duration-300 ${
																					isOpen ? "rotate-180" : ""
																				}`}
																				fill="none"
																				stroke="currentColor"
																				viewBox="0 0 24 24"
																			>
																				<path
																					strokeLinecap="round"
																					strokeLinejoin="round"
																					strokeWidth={2}
																					d="M19 9l-7 7-7-7"
																				/>
																			</svg>
																		)}
																	</div>
																</div>

																{/* 折叠内容 */}
																<Collapse open={isOpen}>
																	<div className="bg-gray-100 p-4">
																		{shipping &&
																			shipping.map((item) => (
																				<div
																					key={item.id}
																					onClick={() => {
																						setIsOpen(false);
																						setactiveshipping(item);
																					}}
																					className={clsx(
																						"mb-4 box-border cursor-pointer border-b-[1px] border-gray-200 p-4",
																						"transition-colors duration-200 hover:bg-gray-50",
																						"relative", // 为选中标记添加定位下文
																						{
																							"bg-blue-50": activeshipping?.id === item.id, // 选中项的背景色
																						},
																					)}
																				>
																					{/* 选中标记 */}
																					{activeshipping?.id === item.id && (
																						<div className="absolute -left-1 top-1/2 h-12 w-1 -translate-y-1/2 rounded-r bg-blue-500" />
																					)}

																					{/* 默认地址标记 */}
																					{item.isDefaultShippingAddress && (
																						<span className="absolute right-4 top-4 rounded-full bg-blue-100 px-2 py-0.5 text-xs text-blue-600">
																							{t("common.default")}
																						</span>
																					)}

																					{/* 收件人姓名 */}
																					<div className="mb-1 flex items-center gap-2 text-base font-medium">
																						{item.firstName} {item.lastName}
																					</div>

																					{/* 公司 - 街道地址 */}
																					<div className="mb-2 text-sm text-gray-600">
																						<span className="mb-2">
																							{item.companyName} {item.streetAddress1}
																						</span>
																						<span className="mb-2 block">
																							{[item.city, item.countryArea, item.postalCode]
																								.filter(Boolean)
																								.join(", ")}{" "}
																							{item.country.country}
																						</span>
																					</div>

																					{/* 电话号码 */}
																					<div className=" text-sm text-gray-500">
																						{t("common.Phone")}: {item.phone}
																					</div>

																					{/* 选中标记（对勾） */}
																					{activeshipping?.id === item.id && (
																						<div className="absolute right-4 top-1/2 -translate-y-1/2">
																							<svg
																								className="h-5 w-5 text-blue-500"
																								fill="none"
																								stroke="currentColor"
																								viewBox="0 0 24 24"
																							>
																								<path
																									strokeLinecap="round"
																									strokeLinejoin="round"
																									strokeWidth={2}
																									d="M5 13l4 4L19 7"
																								/>
																							</svg>
																						</div>
																					)}
																				</div>
																			))}
																	</div>
																</Collapse>
															</>
														) : (
															<>
																<VisitorAddress setactiveshipping={setactiveshipping} />
															</>
														)}

														<div className="mb-5 w-full">
															<div className="sticky top-2 overflow-hidden py-6">
																{isCart ? (
																	// 购物车数据
																	// @ts-ignore
																	<CartContent layout="col" showButton={false} showOrderSum={false} />
																) : (
																	// 立即购买数据
																	// @ts-ignore
																	<CartContentBuyBow layout="col" showButton={false} showOrderSum={false} />
																)}
															</div>
														</div>

														<form onSubmit={handleSubmit(onSubmit)}>
															<div>
																<>
																	{/*支付方式*/}
																	<PayMeth
																		onRadioChange={(val) => {
																			setPaymentMethod(val.id);

																			setValue("payment_method", val);
																		}}
																	/>

																	{/*优惠券*/}
																	<Coupon Cartdata={isCart ? cartList : BuyBowCartList} />

																	{isCart ? (
																		// 订单的总价
																		<OrderSum orderSummary={cartList?.totalPrice?.gross.amount || 0} />
																	) : (
																		// 立即购买数据
																		// 订单的总价
																		<OrderSum orderSummary={BuyBowCartList?.totalPrice?.gross.amount || 0} />
																	)}
																</>

																{/* Order Button */}
																<div className="flex items-end justify-end">
																	<button
																		id="buy-button"
																		type="submit"
																		className={`${
																			activeshipping
																				? loading
																					? "cursor-not-allowed bg-gray-800 text-white shadow-md"
																					: "shadow-4xl cursor-pointer bg-main text-white hover:opacity-70"
																				: "cursor-not-allowed bg-main text-white opacity-50"
																		} font-Roboto mt-6 flex items-center justify-center gap-4 rounded-md px-7 py-4 text-base font-semibold capitalize transition-all duration-300 ease-in-out`}
																		disabled={!activeshipping && !loading}
																	>
																		{loading
																			? t("order.Processing") + "..."
																			: showForm
																				? t("form.89d6993324b64541c93976dbb5de7af086c0")
																				: t("message.cfe62e95200fa44dc3f8874281d7421873f4")}
																		{loading && <RingLoader color="#fff" size={20} />}
																	</button>
																</div>

																<AccountInfo cOrderId={""} email={contactInfo.email || ""} />
															</div>
														</form>
													</div>
												</div>
											</div>
										</div>
									) : (
										<EmptyState />
									)}
								</div>
							),
						},
						{
							id: 2,
							name: t("checkout.f280da5593e54d4dcccbe7a9af91f97d29399"),
							active: activeId === 2,
							status: stepsStatus(2),
							children: (
								<div className="c-flex container flex-col pb-28 pt-8">
									{/* @ts-ignore */}
									<Airwallex AirwallexDetail={AirwallexDetail} AirwallexCallback={AirwallexCallback} />
								</div>
							),
						},
						{
							id: 3,
							name: t("checkout.f280da5593e54d4dcccbe7a9af91f97d2939"),
							active: activeId === 3,
							status: stepsStatus(3),
							children: (
								<div className="c-flex container flex-col pb-28 pt-8">
									<Placeholder
										src="/image/checkout/online_payment.png"
										imageWidth={500}
										imageHeight={400}
										alt="Online payment"
										className="!bg-transparent"
									/>
									<BodyText className="mr-2" size="lg">
										{orderStatusText}
									</BodyText>
									<AccountInfo cOrderId={""} email={contactInfo.email || ""} />
								</div>
							),
						},
					]}
				/>
			</div>
		</div>
	);
}

export default React.memo(Index);
