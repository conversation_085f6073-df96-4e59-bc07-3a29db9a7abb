'use client'
import React from 'react';
import { useTranslations } from "next-intl";
import { type Blog } from '@/lib/@types/api/blog';
import { Link } from "@/navigation";

interface CategoriesProps {
	clsList: Blog.BlogCl[];
	isShowcount?:boolean
}

const Categories: React.FC<CategoriesProps> = ({ clsList ,isShowcount=true}) => {
	const t =useTranslations()

	return (
		<div className="filter-category md:mt-10 mt-6 pb-8 border-b border-line">
			<div className="heading6">{t('blog.Categories')}</div>
			<div className="list-cate pt-1 max-h-[500px] max-md:max-h-[300px] pr-3 overflow-y-auto">
				{clsList.length>0&&clsList.map((item) => (
					<Link href={`/category/${item.cls_slug}`}
						key={item.cls_id}
						className={`cate-item flex items-center justify-between cursor-pointer mt-3 `}

					>
						<div className="capitalize has-line-before hover:text-black text-secondary2 text-gray-500">{item.cls_name}</div>
						{
							isShowcount&&<div className="text-secondary2 text-gray-600">({item.cls_related_count})</div>
						}
					</Link>
				))}
			</div>
		</div>
	);
};

export default Categories;
