"use client";
import React from 'react';
import { Link } from '@/navigation';
import { useTranslations } from 'next-intl';
import { defaultLocale } from '@/config';

interface ProductBreadcrumbProps {
  parentCategory?: any;
  currentTitle: string;
  locale: string;
  isProductsPage?: boolean;
  productName?: string;
  currentCategory?: any; // 添加当前分类信息
}

const ProductBreadcrumb: React.FC<ProductBreadcrumbProps> = ({
  parentCategory,
  currentTitle,
  locale,
  isProductsPage = false,
  productName,
  currentCategory
}) => {
  const t = useTranslations();

  return (
    <div className="py-4">
      <nav className="flex items-center space-x-2 text-sm">
        {/* Home 链接 */}
        <Link
          href="/"
          className="text-gray-600 transition-colors hover:text-black"
        >
          {t("menu.Home")}
        </Link>
        <span className="text-gray-400">/</span>

        {isProductsPage ? (
          <>
            {/* 如果是二级分类，显示一级分类链接 */}
            {parentCategory ? (
              <>
                <Link
                  href={`/products/${parentCategory.slug}`}
                  className="text-gray-600 transition-colors hover:text-black"
                >
                  {locale === defaultLocale ? 
                    parentCategory.name : 
                    (parentCategory.translation?.name || parentCategory.name)
                  }
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-gray-900">
                  {currentTitle}
                </span>
              </>
            ) : (
              /* 如果是一级分类，直接显示当前分类名 */
              <span className="text-gray-900">
                {currentTitle}
              </span>
            )}
          </>
        ) : (
          /* 产品详情页面的面包屑 */
          <>
            {/* 如果有父分类，显示完整路径 */}
            {parentCategory ? (
              <>
                <Link
                  href={`/products/${parentCategory.slug}`}
                  className="text-gray-600 transition-colors hover:text-black"
                >
                  {locale === defaultLocale ?
                    parentCategory.name :
                    (parentCategory.translation?.name || parentCategory.name)
                  }
                </Link>
                <span className="text-gray-400">/</span>
                <Link
                  href={`/products/${currentCategory?.slug || ''}`}
                  className="text-gray-600 transition-colors hover:text-black"
                >
                  {currentTitle}
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-gray-900 line-clamp-1 max-md:max-w-[200px]">
                  {productName}
                </span>
              </>
            ) : (
              /* 如果只有一级分类 */
              <>
                <Link
                  href={`/products/${currentCategory?.slug || ''}`}
                  className="text-gray-600 transition-colors hover:text-black"
                >
                  {currentTitle}
                </Link>
                <span className="text-gray-400">/</span>
                <span className="text-gray-900 line-clamp-1 max-md:max-w-[200px]">
                  {productName}
                </span>
              </>
            )}
          </>
        )}
      </nav>
    </div>
  );
};

export default ProductBreadcrumb;
