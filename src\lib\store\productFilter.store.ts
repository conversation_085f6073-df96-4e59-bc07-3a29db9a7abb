import { create } from 'zustand';

// 定义筛选条件的类型
export interface FilterState {
  availability: string[];
  price: [number, number];
  material: string[];
  coreTechnology: string[];
  edgeGuard: string[];
  thickness: string[];
  weight: string[];
  performanceType: string[];
  subcategory: string[]; // 添加二级分类筛选
}

// 定义筛选状态管理的接口
interface FilterStore {
  filters: FilterState;
  isFilterActive: boolean;
  // 设置筛选条件
  setFilter: <K extends keyof FilterState>(filterType: K, value: FilterState[K]) => void;
  // 清空所有筛选条件
  clearAllFilters: () => void;
  // 获取活跃的筛选条件数量
  getActiveFilterCount: () => number;
  // 路由变化时重置状态
  resetFilterState: () => void;
}

// 初始筛选条件
const initialFilters: FilterState = {
  availability: [],
  price: [0, 180],
  material: [],
  coreTechnology: [],
  edgeGuard: [],
  thickness: [],
  weight: [],
  performanceType: [],
  subcategory: [], // 添加二级分类筛选初始值
};

// 创建筛选条件的状态管理器
export const useProductFilterStore = create<FilterStore>((set, get) => ({
  filters: initialFilters,
  isFilterActive: false,
  
  // 设置筛选条件
  setFilter: (filterType, value) => {
    set((state) => {
      const newFilters = {
        ...state.filters,
        [filterType]: value,
      };
      
      // 检查是否有任何筛选条件被激活
      const isActive = Object.entries(newFilters).some(([key, value]) => {
        if (key === 'price') {
          return (value as [number, number])[0] > 0 || (value as [number, number])[1] < 180;
        }
        return (value as string[]).length > 0;
      });
      
      return {
        filters: newFilters,
        isFilterActive: isActive,
      };
    });
  },
  
  // 清空所有筛选条件
  clearAllFilters: () => {
    set({
      filters: initialFilters,
      isFilterActive: false,
    });
  },
  
  // 路由变化时重置状态
  resetFilterState: () => {
    set({
      filters: initialFilters,
      isFilterActive: false,
    });
  },
  
  // 获取活跃的筛选条件数量
  getActiveFilterCount: () => {
    const { filters } = get();
    let count = 0;
    
    Object.entries(filters).forEach(([key, value]) => {
      if (key === 'price') {
        if ((value as [number, number])[0] > 0 || (value as [number, number])[1] < 180) {
          count += 1;
        }
      } else {
        count += (value as string[]).length;
      }
    });
    
    return count;
  },
}));
