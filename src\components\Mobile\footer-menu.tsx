"use client"
import { BodyText } from "@/components/BodyText";
import { Link } from "@/navigation";
import { useState } from "react";

import LoginModal from "@/components/User/login-modal";

import { useTranslations } from "next-intl";
import {useRouter  } from "@/navigation";
import { useUserStore } from "@/store/user.store";

import  CartDrawer  from "@/components/Product/ShoppingCart/cartDrawer";

export default function FooterMenu() {
  const t = useTranslations();
  const router = useRouter();
  const { userInfo, getUserInfo } = useUserStore();

  const [activeId, setActiveId] = useState(1);

  const [openModal, setOpenModal] = useState(false);
  // const { isBtob } = siteStore();


  function openCartCallBack(){
    return null

  }

  const jumpCenter = () => {
    if (userInfo) {
      router.push("/center");
    } else {
      setActiveId(1);
      setOpenModal(true);
    }
  };

  return <div className={`fixed bottom-0 left-0 right-0 block md:hidden `} style={{
    zIndex: "99"
  }}>
    <div className="container h-mfnav-h bg-white grid grid-cols-5 border-t box-border px-[16px]">
      <Link href="/" className="c-flex flex-col py-[16px] text-[#000000]">
        <i className="ri-home-3-line ri-xl"></i>
        <BodyText intent="bold" className="pt-1 !text-sm">{t("common.Home")}</BodyText>
      </Link>


      <Link href="/products" className="c-flex flex-col text-[#000000]">
        <i className="ri-store-2-line ri-xl"></i>
        <BodyText intent="bold" className="pt-1 !text-sm">{t("common.Product")}</BodyText>
      </Link>


      <div className="c-flex flex-col py-[16px] text-[#000000]" onClick={jumpCenter}>
        <i className="ri-map-pin-user-line ri-xl"></i>
        <BodyText intent="bold" className="pt-1 !text-sm">{t("common.Center")}</BodyText>
      </div>

      <Link href="/contact-us" className="c-flex flex-col text-[#000000]">
        <i className="ri-contacts-book-2-line ri-xl"></i>
        <BodyText intent="bold" className="pt-1 !text-sm">{t("common.Contact")}</BodyText>
      </Link>

      {
        process.env.NEXT_PUBLIC_SITE_TYPE == "tob"&&<Link href="/inquiry" className="c-flex flex-col py-[16px] text-[#000000]">
        <i className="ri-file-list-2-line ri-xl"></i>
        <BodyText intent="bold" className="pt-1 !text-sm">{t("common.Inquiry")} </BodyText>
      </Link>
      }

      {
        process.env.NEXT_PUBLIC_SITE_TYPE == "toc"&&<CartDrawer className="c-flex flex-col relative" callback={openCartCallBack}>
        <i className={`ri-shopping-bag-line ri-xl relative`}>
          <div className="text-xs absolute top-[-8px] right-[-2px] p-1 bg-[#f44336] rounded-full"></div>
        </i>
        <BodyText intent="bold" className="pt-1 !text-sm">{
          t("common.Cart")
        }</BodyText>
      </CartDrawer>
      }

      

    </div>
    <LoginModal activeId={activeId} setActiveId={setActiveId} openModal={openModal} setOpenModal={setOpenModal} />
  </div>;
}
