"use client"
import React, { useEffect, useRef } from 'react';
import { Drawer, Collapse, Checkbox, Space, Slider } from 'antd';
import { useTranslations, useLocale } from 'next-intl';
import { SlidersHorizontal } from "@phosphor-icons/react";
import { useProductFilterStore } from '@/lib/store/productFilter.store';
import { usePathname } from 'next/navigation';
import { defaultLocale } from '@/config';
const { Panel } = Collapse;

interface FilterDrawerProps {
  open: boolean;
  onClose: () => void;
  isMobile: boolean;
  secondCategories?: any[]; // 添加二级分类数据
  showSubcategoryFilter?: boolean; // 是否显示二级分类筛选
}

const FilterDrawer: React.FC<FilterDrawerProps> = ({ open, onClose, isMobile, secondCategories, showSubcategoryFilter }) => {
  const t = useTranslations();
  const pathname = usePathname(); // 获取当前路径
  const locale = useLocale()
  const parts = pathname.split('/').filter(Boolean);
  const categorySlug = parts[parts.length - 1]; // 使用路径的最后一个部分作为 slug，支持 /products/xxx 或 /[locale]/products/xxx

  // 筛选面板映射：根据 slug 显示特定面板
  const filterPanelsBySlug: Record<string, string[]> = {
    'paddle': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'usap-approved': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'axis-series': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'nova-series': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'pulse-series': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'ip-collection': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'social-set': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
    'quasar-series': [
      'material',
      'coreTechnology',
      'edgeGuard',
      'thickness',
      'weight',
      'performanceType'
    ],
  };

  // 判断是否为球拍相关的slug（这些slug使用"series"标签）
  const isPaddleCategory = [
    'paddle',
    'usap-approved',
    'axis-series',
    'nova-series',
    'pulse-series',
    'ip-collection',
    'social-set',
    'quasar-series'
  ].includes(categorySlug);

  // 获取当前 slug 的面板列表
  const activePanels = filterPanelsBySlug[categorySlug] || [];

  // 使用筛选状态管理器
  const {
    filters,
    setFilter,
    clearAllFilters,
    getActiveFilterCount,
    resetFilterState
  } = useProductFilterStore();

  const filterCount = getActiveFilterCount();

  // 保存上一次路径的ref，用于检测路由变化
  const prevPathRef = useRef(pathname);

  // 监听路由变化，重置筛选状态
  useEffect(() => {
    if (pathname !== prevPathRef.current) {
      // 路径变化了，重置筛选状态
      resetFilterState();
      prevPathRef.current = pathname;
    }
  }, [pathname, resetFilterState]);

  // 价格滑块的格式化函数
  const formatPrice = (value: number) => `$${value}`;

  return (
    <Drawer
      title={
        <div className="flex items-center gap-2 border-b border-[#e8e8e8] pb-4">
          <SlidersHorizontal size={32} color='#4c4c4c' />
          <span className="text-xl font-semibold">{t('common.Filter')}</span>
          {filterCount > 0 && (
            <span className="ml-2 px-2 py-1 bg-black text-white rounded-full text-xs">
              {filterCount}
            </span>
          )}
        </div>
      }
      placement={"left"}
      onClose={onClose}
      open={open}
      width={isMobile ? "86vw" : '23vw'}
      closable={true}
      closeIcon={
        <div className="h-8 w-8 flex items-center justify-center rounded-full  text-black transition-all">
          <svg xmlns="http://www.w3.org/2000/svg" width="35" height="35" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </div>
      }
      footer={
        <div className="flex justify-between py-4  gap-4">
          <button
            onClick={clearAllFilters}
            className="bg-white border border-gray-300 py-3 hover:border-[#83c000] hover:bg-[#83c000] hover:text-white text-black font-medium flex items-center justify-center"
            style={{ width: 'calc(50% - 8px)' }}
          >
            {t('common.Clear Filters')}
          </button>
          <button
            onClick={onClose}
            className="bg-white border border-gray-300 py-3 hover:border-[#83c000] hover:bg-[#83c000] hover:text-white text-black font-medium flex items-center justify-center"
            style={{ width: 'calc(50% - 8px)' }}
          >
            {t('common.View')} &nbsp;
            {/* {filterCount > 0 ? `(${filterCount}) ` : ''} */}
            {t('common.Results')}
          </button>
        </div>
      }
      className="filter-drawer"
    >
      <div className="">
        <Collapse
          defaultActiveKey={['availability']}
          ghost
          expandIconPosition="end"
          className="filter-collapse"
          expandIcon={({ isActive }) => (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#828282"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              style={{ transform: isActive ? 'rotate(180deg)' : 'rotate(0deg)' }}
            >
              <polyline points="6 9 12 15 18 9"></polyline>
            </svg>
          )}
        >
          {/* 1. Availability */}
          <Panel header={<span className="text-base text-black">{t('common.Availability')}</span>} key="availability">
            <Checkbox.Group
              onChange={(values) => setFilter('availability', values as string[])}
              value={filters.availability}
              className="checkbox-group-large"
            >
              <Space direction="vertical" className="w-full" size={16}>
                <Checkbox value="inStock" className="checkbox-large !text-black">{t('common.In Stock')}</Checkbox>
                <Checkbox value="outOfStock" className="checkbox-large !text-black">{t('common.Out of Stock')}</Checkbox>
              </Space>
            </Checkbox.Group>
          </Panel>
          {/* 2. Price (USD) */}
          <Panel header={<span className="text-base text-black">{t('common.Price')}</span>} key="price">
            <div className="px-2 py-6 2xl:w-[70%]">
              {/* 自定义滑块样式 */}
              <div className="mb-8">
                <Slider
                  range
                  min={0}
                  max={180}
                  defaultValue={[0, 180]}
                  tipFormatter={null}
                  value={filters.price}
                  onChange={(value) => setFilter('price', value as [number, number])}
                  className="price-range-slider"
                />
              </div>
              {/* 价格输入框 */}
              <div className="flex items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="bg-[#ededed] px-4 py-2 rounded-full  text-[#585858] font-medium text-base">
                    $ {filters.price[0] === 0 ? '0' : filters.price[0]}
                  </div>
                </div>
                <div className="text-black font-medium text-base">
                  To
                </div>
                <div className="flex-1">
                  <div className="bg-[#ededed] px-4 py-2 rounded-full  text-[#585858] font-medium text-base">
                    $ {filters.price[1]}.0
                  </div>
                </div>
              </div>
            </div>
          </Panel>

          {/* 3. 二级分类筛选 - 根据类别显示不同标签 */}
          {showSubcategoryFilter && secondCategories && secondCategories.length > 0 && (
            <Panel
              header={
                <span className="text-base text-black">
                  {isPaddleCategory ? t('common.Series') : t('common.Category')}
                </span>
              }
              key="subcategory"
            >
              <Checkbox.Group
                onChange={(values) => setFilter('subcategory', values as string[])}
                value={filters.subcategory}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  {secondCategories.map((cat: any) => (
                    <Checkbox key={cat.node.slug} value={cat.node.slug} className="checkbox-large !text-black">
                      {locale === defaultLocale ? cat.node.name.replace(/[‘’“”]/g, (match) => (match === '“' || match === '”' ? '"' : "'")) : (cat.node.translation?.name || cat.node.name).replace(/[‘’“”]/g, (match) => (match === '“' || match === '”' ? '"' : "'"))}
                    </Checkbox>
                  ))}
                </Space>
              </Checkbox.Group>
            </Panel>
          )}

          {/* 4. Main Material */}
          {activePanels.includes('material') && (
            <Panel header={<span className="text-base text-black">{t('common.Main Material')}</span>} key="material">
              <Checkbox.Group
                onChange={(values) => setFilter('material', values as string[])}
                value={filters.material}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  <Checkbox value="Fiberglass" className="checkbox-large !text-black">{t('common.Fiberglass')}</Checkbox>
                  <Checkbox value="Composite" className="checkbox-large !text-black">{t('common.Composite')}</Checkbox>
                  <Checkbox value="Carbon Fiber" className="checkbox-large !text-black">{t('common.Carbon Fiber')}</Checkbox>
                </Space>
              </Checkbox.Group>
            </Panel>
          )}

          {/* 5. Core Technology */}
          {activePanels.includes('coreTechnology') && (
            <Panel header={<span className="text-base text-black">{t('common.Core Technology')}</span>} key="coreTechnology">
              <Checkbox.Group
                onChange={(values) => setFilter('coreTechnology', values as string[])}
                value={filters.coreTechnology}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  <Checkbox value="PP Honeycomb" className="checkbox-large !text-black">{t('common.PP Honeycomb')}</Checkbox>
                  <Checkbox value="Aramid" className="checkbox-large !text-black">{t('common.Aramid')}</Checkbox>
                  <Checkbox value="DNC" className="checkbox-large !text-black">{t('common.DNC')}</Checkbox>
                  <Checkbox value="EPP" className="checkbox-large !text-black">{t('common.EPP')}</Checkbox>
                </Space>
              </Checkbox.Group>
            </Panel>
          )}

          {/* 6. Edge Guard */}
          {activePanels.includes('edgeGuard') && (
            <Panel header={<span className="text-base text-black">{t('common.Edge Guard')}</span>} key="edgeGuard">
              <Checkbox.Group
                onChange={(values) => setFilter('edgeGuard', values as string[])}
                value={filters.edgeGuard}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  <Checkbox value="Yes" className="checkbox-large !text-black">{t('common.YES')}</Checkbox>
                  <Checkbox value="Edge Guard" className="checkbox-large !text-black">{t('common.Edge Guard')}</Checkbox>
                </Space>
              </Checkbox.Group>
            </Panel>
          )}

          {/* 7. Thickness */}
          {activePanels.includes('thickness') && (
            <Panel header={<span className="text-base text-black">{t('common.Thickness')}</span>} key="thickness">
              <Checkbox.Group
                onChange={(values) => setFilter('thickness', values as string[])}
                value={filters.thickness}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  <Checkbox value="10mm" className="checkbox-large !text-black">{t('common.10mm')}</Checkbox>
                  <Checkbox value="11mm" className="checkbox-large !text-black">{t('common.11mm')}</Checkbox>
                  <Checkbox value="13mm" className="checkbox-large !text-black">{t('common.13mm')}</Checkbox>
                  <Checkbox value="14mm" className="checkbox-large !text-black">{t('common.14mm')}</Checkbox>
                  <Checkbox value="16mm" className="checkbox-large !text-black">{t('common.16mm')}</Checkbox>
                  <Checkbox value="19mm" className="checkbox-large !text-black">{t('common.19mm')}</Checkbox>
                </Space>
              </Checkbox.Group>
            </Panel>
          )}

          {/* 8. Weight */}
          {activePanels.includes('weight') && (
            <Panel header={<span className="text-base text-black">{t('common.Weight')}</span>} key="weight">
              <Checkbox.Group
                onChange={(values) => setFilter('weight', values as string[])}
                value={filters.weight}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  <Checkbox value="≤220g" className="checkbox-large !text-black">{t('common.Under 220g')}</Checkbox>
                  <Checkbox value="220~235g" className="checkbox-large !text-black">{t('common.220-235g')}</Checkbox>
                  <Checkbox value="≥235g" className="checkbox-large !text-black">{t('common.Over 235g')}</Checkbox>
                </Space>
              </Checkbox.Group>
            </Panel>
          )}

          {/* 9. Performance Type */}
          {activePanels.includes('performanceType') && (
            <Panel header={<span className="text-base text-black">{t('common.Performance Type')}</span>} key="performanceType">
              <Checkbox.Group
                onChange={(values) => setFilter('performanceType', values as string[])}
                value={filters.performanceType}
                className="checkbox-group-large"
              >
                <Space direction="vertical" className="w-full" size={16}>
                  <Checkbox value="balanced" className="checkbox-large !text-black">{t('common.Balanced')}</Checkbox>
                  <Checkbox value="control" className="checkbox-large !text-black">{t('common.Control')}</Checkbox>
                  <Checkbox value="power" className="checkbox-large !text-black">{t('common.Power')}</Checkbox>
                </Space>
              </Checkbox.Group>
            </Panel>
          )}
        </Collapse>
      </div>
    </Drawer>
  );
};

export default FilterDrawer; 